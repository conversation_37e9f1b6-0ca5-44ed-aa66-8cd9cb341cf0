{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 14637322225222829], [16702348383442838006, "build_script_build", false, 10403526372000146175], [4155399759509430981, "build_script_build", false, 9057876233514395226]], "local": [{"RerunIfChanged": {"output": "debug\\build\\ysviewer-b7b2d7e16244b136\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": "{\"build\":{\"devUrl\":\"http://127.0.0.1:1430\"}}"}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}