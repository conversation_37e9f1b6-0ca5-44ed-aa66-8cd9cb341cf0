{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 15366803983864524774], [16702348383442838006, "build_script_build", false, 8329639091305372109]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-opener-0f090c6fd5eb58cf\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}