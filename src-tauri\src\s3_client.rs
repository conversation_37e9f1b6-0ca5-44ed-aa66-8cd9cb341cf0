use reqwest;
use std::error::Error as StdError;
use std::fmt;

#[derive(Debug)]
pub struct S3Error(String);

impl fmt::Display for S3Error {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "S3 error: {}", self.0)
    }
}

impl StdError for S3Error {}

pub struct S3Client {
    client: reqwest::Client,
    bucket: String,
    region: String,
}

impl S3Client {
    pub async fn new() -> Result<Self, S3Error> {
        let client = reqwest::Client::new();

        Ok(Self {
            client,
            bucket: "ysviewertests".to_string(),
            region: "eu-west-1".to_string(),
        })
    }

    pub async fn upload_file(&self, key: &str, _content: &str) -> Result<(), S3Error> {
        // Note: Upload should be done via the upload scripts with proper AWS credentials
        // This method is kept for interface compatibility
        println!("Upload should be done via upload scripts. File: {}", key);
        Ok(())
    }

    pub async fn download_file(&self, key: &str) -> Result<String, S3Error> {
        // Access the file via public HTTPS URL
        let public_url = format!("https://{}.s3.{}.amazonaws.com/{}",
            self.bucket, self.region, key);

        println!("Downloading from public URL: {}", public_url);

        match self.client.get(&public_url).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    match response.text().await {
                        Ok(content) => Ok(content),
                        Err(e) => Err(S3Error(format!("Failed to read response: {}", e)))
                    }
                } else {
                    // Fallback to local file if S3 fails
                    match tokio::fs::read_to_string(key).await {
                        Ok(content) => {
                            println!("Using local fallback for {}", key);
                            Ok(content)
                        },
                        Err(_) => Err(S3Error(format!("File {} not found in S3 or locally", key)))
                    }
                }
            }
            Err(e) => {
                // Fallback to local file if network fails
                match tokio::fs::read_to_string(key).await {
                    Ok(content) => {
                        println!("Using local fallback for {} due to network error: {}", key, e);
                        Ok(content)
                    },
                    Err(_) => Err(S3Error(format!("Network error and no local fallback: {}", e)))
                }
            }
        }
    }

    pub async fn file_exists(&self, key: &str) -> Result<bool, S3Error> {
        // Check if file exists via public HTTPS URL
        let public_url = format!("https://{}.s3.{}.amazonaws.com/{}",
            self.bucket, self.region, key);

        match self.client.head(&public_url).send().await {
            Ok(response) => Ok(response.status().is_success()),
            Err(_) => {
                // Fallback to local file check
                Ok(tokio::fs::metadata(key).await.is_ok())
            }
        }
    }
}
