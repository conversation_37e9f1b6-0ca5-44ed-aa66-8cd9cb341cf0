# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-effects"
description = "Enables the set_effects command without any pre-configured scope."
commands.allow = ["set_effects"]

[[permission]]
identifier = "deny-set-effects"
description = "Denies the set_effects command without any pre-configured scope."
commands.deny = ["set_effects"]
