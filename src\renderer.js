const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class YSViewerRenderer {
  constructor() {
    this.isAudioMuted = false;
    this.isMinimized = false;
    this.initializeUI();
    this.setupEventListeners();
    this.loadAppState();
  }

  initializeUI() {
    // Get DOM elements
    this.audioBtn = document.getElementById('audio-btn');
    this.audioIcon = document.getElementById('audio-icon');
    this.audioText = document.getElementById('audio-text');
    this.minimizeBtn = document.getElementById('minimize-btn');
    this.closeBtn = document.getElementById('close-btn');
    this.statusText = document.getElementById('status-text');
    this.notification = document.getElementById('notification');
    this.notificationText = document.getElementById('notification-text');
  }

  setupEventListeners() {
    // Audio control button
    this.audioBtn.addEventListener('click', async () => {
      try {
        this.isAudioMuted = await ipcRenderer.invoke('toggle-audio');
        this.updateAudioButton();
        this.showNotification(
          this.isAudioMuted ? 'System audio muted' : 'System audio unmuted',
          'success'
        );
      } catch (error) {
        console.error('Failed to toggle audio:', error);
        this.showNotification('Failed to toggle audio', 'error');
      }
    });

    // Minimize to dot button
    this.minimizeBtn.addEventListener('click', async () => {
      try {
        await ipcRenderer.invoke('minimize-to-dot');
        this.isMinimized = true;
        this.showNotification('Minimized to dot (50x50)', 'info');
      } catch (error) {
        console.error('Failed to minimize:', error);
        this.showNotification('Failed to minimize', 'error');
      }
    });

    // Close/Hide button
    this.closeBtn.addEventListener('click', () => {
      window.close(); // This will trigger the close event in main process
    });

    // Listen for audio state changes from main process
    ipcRenderer.on('audio-state-changed', (event, { isMuted }) => {
      this.isAudioMuted = isMuted;
      this.updateAudioButton();
    });

    // Listen for playlist updates
    ipcRenderer.on('playlist-updated', (event, data) => {
      this.updateStatus(`Playing: ${data.title || 'Unknown'}`);
      this.showNotification(`Now playing: ${data.platform}`, 'info');
    });

    // Listen for app state changes
    ipcRenderer.on('app-state-changed', (event, state) => {
      this.isAudioMuted = state.isMuted;
      this.isMinimized = state.isMinimized;
      this.updateUI();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'm':
            event.preventDefault();
            this.audioBtn.click();
            break;
          case 'h':
            event.preventDefault();
            this.closeBtn.click();
            break;
          case 'd':
            event.preventDefault();
            this.minimizeBtn.click();
            break;
        }
      }
    });

    // Window focus events
    window.addEventListener('focus', () => {
      if (this.isMinimized) {
        this.isMinimized = false;
        this.showNotification('Window restored', 'info');
      }
    });

    // Prevent context menu on production
    if (!process.argv.includes('--dev')) {
      document.addEventListener('contextmenu', (event) => {
        event.preventDefault();
      });
    }
  }

  async loadAppState() {
    try {
      const state = await ipcRenderer.invoke('get-app-state');
      this.isAudioMuted = state.isMuted;
      this.isMinimized = state.isMinimized;
      this.updateUI();
      this.updateStatus('Application loaded');
    } catch (error) {
      console.error('Failed to load app state:', error);
      this.updateStatus('Failed to load state');
    }
  }

  updateUI() {
    this.updateAudioButton();
    this.updateMinimizeButton();
  }

  updateAudioButton() {
    if (this.isAudioMuted) {
      this.audioIcon.textContent = '🔇';
      this.audioText.textContent = 'Unmute';
      this.audioBtn.classList.add('active');
      this.audioBtn.title = 'Unmute System Audio';
    } else {
      this.audioIcon.textContent = '🔊';
      this.audioText.textContent = 'Mute';
      this.audioBtn.classList.remove('active');
      this.audioBtn.title = 'Mute System Audio';
    }
  }

  updateMinimizeButton() {
    if (this.isMinimized) {
      this.minimizeBtn.classList.add('active');
      this.minimizeBtn.title = 'Restore Window';
    } else {
      this.minimizeBtn.classList.remove('active');
      this.minimizeBtn.title = 'Minimize to Dot (50x50)';
    }
  }

  updateStatus(message) {
    this.statusText.textContent = message;
    console.log('Status:', message);
  }

  showNotification(message, type = 'info') {
    this.notificationText.textContent = message;
    this.notification.className = `notification ${type}`;
    this.notification.classList.add('show');

    // Auto-hide after 3 seconds
    setTimeout(() => {
      this.notification.classList.remove('show');
    }, 3000);
  }

  // Utility methods for external access
  async toggleAudio() {
    this.audioBtn.click();
  }

  async minimizeToDot() {
    this.minimizeBtn.click();
  }

  async hideWindow() {
    this.closeBtn.click();
  }
}

// Initialize the renderer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.ysviewer = new YSViewerRenderer();
  
  // Show welcome message
  setTimeout(() => {
    window.ysviewer.showNotification('YSViewer loaded successfully!', 'success');
  }, 1000);
});

// Handle unhandled errors
window.addEventListener('error', (event) => {
  console.error('Renderer error:', event.error);
  if (window.ysviewer) {
    window.ysviewer.showNotification('An error occurred', 'error');
  }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  if (window.ysviewer) {
    window.ysviewer.showNotification('An error occurred', 'error');
  }
});
