const { app, <PERSON>rows<PERSON><PERSON><PERSON>ow, Browser<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u, ipc<PERSON>ain, screen, shell } = require('electron');
const path = require('path');
const Store = require('electron-store');
const AutoLaunch = require('auto-launch');
const PlaylistManager = require('./playlist-manager');
const AudioController = require('./audio-controller');

// Initialize store for persistent data
const store = new Store();

// Global references
let mainWindow = null;
let tray = null;
let youtubeView = null;
let spotifyView = null;
let isMinimized = false;
let isMuted = false;
let playlistManager = null;
let audioController = null;

// Auto-launch setup
const autoLauncher = new AutoLaunch({
  name: 'YSViewer',
  path: app.getPath('exe')
});

class YSViewerApp {
  constructor() {
    this.initializeApp();
  }

  async initializeApp() {
    // Initialize managers
    playlistManager = new PlaylistManager();
    audioController = new AudioController();

    // Setup auto-launch
    await this.setupAutoLaunch();

    // Create main window
    this.createMainWindow();

    // Create system tray
    this.createSystemTray();

    // Setup browser views
    this.setupBrowserViews();

    // Start playlist management
    await this.startPlaylistManagement();
  }

  async setupAutoLaunch() {
    try {
      const isEnabled = await autoLauncher.isEnabled();
      if (!isEnabled) {
        await autoLauncher.enable();
        console.log('Auto-launch enabled');
      }
    } catch (error) {
      console.error('Failed to setup auto-launch:', error);
    }
  }

  createMainWindow() {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    mainWindow = new BrowserWindow({
      width: Math.min(1400, width - 100),
      height: Math.min(800, height - 100),
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        webSecurity: false
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      show: false,
      frame: true,
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
    });

    // Load the main HTML file
    mainWindow.loadFile(path.join(__dirname, 'electron-index.html'));

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
      mainWindow.show();
      if (process.argv.includes('--dev')) {
        mainWindow.webContents.openDevTools();
      }
    });

    // Handle window close
    mainWindow.on('close', (event) => {
      if (!app.isQuiting) {
        event.preventDefault();
        this.minimizeToTray();
      }
    });

    // Handle window minimize
    mainWindow.on('minimize', () => {
      this.minimizeToTray();
    });
  }

  createSystemTray() {
    const iconPath = process.platform === 'win32' 
      ? path.join(__dirname, '../assets/icon.ico')
      : path.join(__dirname, '../assets/icon.png');

    tray = new Tray(iconPath);

    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'YSViewer',
        type: 'normal',
        enabled: false
      },
      { type: 'separator' },
      {
        label: isMuted ? 'Unmute System Audio' : 'Mute System Audio',
        type: 'normal',
        click: () => this.toggleSystemAudio()
      },
      { type: 'separator' },
      {
        label: 'Show Window',
        type: 'normal',
        click: () => this.showMainWindow()
      },
      {
        label: 'Minimize to Dot',
        type: 'normal',
        click: () => this.minimizeToDot()
      },
      { type: 'separator' },
      {
        label: 'Quit',
        type: 'normal',
        click: () => {
          app.isQuiting = true;
          app.quit();
        }
      }
    ]);

    tray.setContextMenu(contextMenu);
    tray.setToolTip('YSViewer - YouTube & Spotify Player');

    // Handle tray click
    tray.on('click', () => {
      if (mainWindow.isVisible()) {
        this.minimizeToTray();
      } else {
        this.showMainWindow();
      }
    });
  }

  setupBrowserViews() {
    const bounds = mainWindow.getBounds();
    const viewWidth = Math.floor(bounds.width / 2);
    const viewHeight = bounds.height - 100; // Leave space for controls

    // YouTube view
    youtubeView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.setBrowserView(youtubeView);
    youtubeView.setBounds({ x: 0, y: 100, width: viewWidth, height: viewHeight });
    youtubeView.webContents.loadURL('https://www.youtube.com');

    // Spotify view
    spotifyView = new BrowserView({
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    mainWindow.addBrowserView(spotifyView);
    spotifyView.setBounds({ x: viewWidth, y: 100, width: viewWidth, height: viewHeight });
    spotifyView.webContents.loadURL('https://open.spotify.com');

    // Handle window resize
    mainWindow.on('resize', () => {
      const newBounds = mainWindow.getBounds();
      const newViewWidth = Math.floor(newBounds.width / 2);
      const newViewHeight = newBounds.height - 100;

      youtubeView.setBounds({ x: 0, y: 100, width: newViewWidth, height: newViewHeight });
      spotifyView.setBounds({ x: newViewWidth, y: 100, width: newViewWidth, height: newViewHeight });
    });

    // Handle external links
    [youtubeView, spotifyView].forEach(view => {
      view.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
      });
    });
  }

  async startPlaylistManagement() {
    try {
      await playlistManager.initialize();
      
      // Start playing immediately
      setTimeout(() => {
        this.playNextVideo();
      }, 3000); // Wait 3 seconds for views to load

      // Set up periodic playlist checking
      setInterval(() => {
        this.checkAndPlayNext();
      }, 3600000); // Check every hour
    } catch (error) {
      console.error('Failed to start playlist management:', error);
    }
  }

  async playNextVideo() {
    try {
      const nextVideo = await playlistManager.getNextMandatoryVideo();
      if (nextVideo) {
        console.log('Playing next video:', nextVideo.url);
        
        if (nextVideo.url.includes('youtube.com') || nextVideo.url.includes('youtu.be')) {
          youtubeView.webContents.loadURL(nextVideo.url);
        } else if (nextVideo.url.includes('spotify.com')) {
          spotifyView.webContents.loadURL(nextVideo.url);
        }
      }
    } catch (error) {
      console.error('Failed to play next video:', error);
    }
  }

  async checkAndPlayNext() {
    try {
      const shouldPlay = await playlistManager.shouldPlayNext();
      if (shouldPlay) {
        this.playNextVideo();
      }
    } catch (error) {
      console.error('Failed to check playlist:', error);
    }
  }

  async toggleSystemAudio() {
    try {
      isMuted = await audioController.toggleMute();
      this.updateTrayMenu();
      
      // Notify renderer process
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('audio-state-changed', { isMuted });
      }
    } catch (error) {
      console.error('Failed to toggle audio:', error);
    }
  }

  updateTrayMenu() {
    if (tray && !tray.isDestroyed()) {
      const contextMenu = Menu.buildFromTemplate([
        {
          label: 'YSViewer',
          type: 'normal',
          enabled: false
        },
        { type: 'separator' },
        {
          label: isMuted ? 'Unmute System Audio' : 'Mute System Audio',
          type: 'normal',
          click: () => this.toggleSystemAudio()
        },
        { type: 'separator' },
        {
          label: 'Show Window',
          type: 'normal',
          click: () => this.showMainWindow()
        },
        {
          label: 'Minimize to Dot',
          type: 'normal',
          click: () => this.minimizeToDot()
        },
        { type: 'separator' },
        {
          label: 'Quit',
          type: 'normal',
          click: () => {
            app.isQuiting = true;
            app.quit();
          }
        }
      ]);
      tray.setContextMenu(contextMenu);
    }
  }

  showMainWindow() {
    if (mainWindow) {
      if (isMinimized) {
        // Restore from minimized state
        mainWindow.setSize(1400, 800);
        mainWindow.center();
        isMinimized = false;
      }
      mainWindow.show();
      mainWindow.focus();
    }
  }

  minimizeToTray() {
    if (mainWindow) {
      mainWindow.hide();
    }
  }

  minimizeToDot() {
    if (mainWindow) {
      const { width, height } = screen.getPrimaryDisplay().workAreaSize;
      mainWindow.setSize(50, 50); // Changed from 5x5 to 50x50
      mainWindow.setPosition(width - 50, height - 50);
      mainWindow.setAlwaysOnTop(true);
      mainWindow.setSkipTaskbar(true);
      isMinimized = true;
    }
  }
}

// App event handlers
app.whenReady().then(() => {
  new YSViewerApp();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    new YSViewerApp();
  }
});

// IPC handlers
ipcMain.handle('get-app-state', () => {
  return {
    isMuted,
    isMinimized
  };
});

ipcMain.handle('toggle-audio', async () => {
  await new YSViewerApp().toggleSystemAudio();
  return isMuted;
});

ipcMain.handle('minimize-to-dot', () => {
  new YSViewerApp().minimizeToDot();
});

ipcMain.handle('show-window', () => {
  new YSViewerApp().showMainWindow();
});
