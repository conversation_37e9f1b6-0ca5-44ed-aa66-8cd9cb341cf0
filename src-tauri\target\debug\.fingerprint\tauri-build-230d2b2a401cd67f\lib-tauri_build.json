{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 6936958328004180934, "deps": [[2671782512663819132, "tauri_utils", false, 17972510036500744830], [4899080583175475170, "semver", false, 6395737144516353431], [6913375703034175521, "schemars", false, 6206245101019458460], [7170110829644101142, "json_patch", false, 9674569900180521461], [9689903380558560274, "serde", false, 7128043410522221942], [12714016054753183456, "tauri_winres", false, 10073389857308142167], [13077543566650298139, "heck", false, 8423826346643455051], [13475171727366188400, "cargo_toml", false, 9678564345250968215], [13625485746686963219, "anyhow", false, 13352825885805927492], [15367738274754116744, "serde_json", false, 9701281561460897278], [15609422047640926750, "toml", false, 15626852650080321713], [15622660310229662834, "walkdir", false, 3834500685285115626], [16928111194414003569, "dirs", false, 2297253392593602267], [17155886227862585100, "glob", false, 9965666631543504377]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-230d2b2a401cd67f\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}