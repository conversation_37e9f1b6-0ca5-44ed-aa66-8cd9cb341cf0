import React, { useState, useCallback, useEffect, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { getCurrentWindow } from '@tauri-apps/api/window';
import { listen } from '@tauri-apps/api/event';
import './App.css';

interface WindowState {
  isMinimized: boolean;
  isMuted: boolean;
}

interface VideoEntry {
  url: string;
  platform: 'YouTube' | 'Spotify';
  play_count_today: number;
  last_played?: string;
  duration_watched: number;
}

const App: React.FC = () => {
  const [windowState, setWindowState] = useState<WindowState>({
    isMinimized: false,
    isMuted: false
  });
  const [error, setError] = useState<string | null>(null);
  const [loadingStates, setLoadingStates] = useState({
    youtube: true,
    spotify: true
  });
  const [currentVideo, setCurrentVideo] = useState<VideoEntry | null>(null);
  const [autoPlay, setAutoPlay] = useState(true);

  const youtubeRef = useRef<HTMLIFrameElement>(null);
  const spotifyRef = useRef<HTMLIFrameElement>(null);

  // Load initial window state
  useEffect(() => {
    const loadWindowState = async () => {
      try {
        const state = await invoke<WindowState>('get_window_state');
        setWindowState(state);
      } catch (err) {
        console.error('Failed to load window state:', err);
        setError('Failed to load window state');
      }
    };

    loadWindowState();
  }, []);

  // Listen for automated video playback events
  useEffect(() => {
    const setupEventListeners = async () => {
      const unlisten = await listen<VideoEntry>('play_video', (event) => {
        const video = event.payload;
        setCurrentVideo(video);
        playVideo(video);
      });

      return unlisten;
    };

    setupEventListeners();
  }, []);

  const playVideo = useCallback((video: VideoEntry) => {
    if (video.platform === 'YouTube') {
      // Update YouTube iframe src
      if (youtubeRef.current) {
        const videoId = extractYouTubeVideoId(video.url);
        if (videoId) {
          youtubeRef.current.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&enablejsapi=1`;
        }
      }
    } else if (video.platform === 'Spotify') {
      // Update Spotify iframe src
      if (spotifyRef.current) {
        const embedUrl = convertSpotifyToEmbed(video.url);
        if (embedUrl) {
          spotifyRef.current.src = embedUrl;
        }
      }
    }
  }, []);

  const extractYouTubeVideoId = (url: string): string | null => {
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/);
    return match ? match[1] : null;
  };

  const convertSpotifyToEmbed = (url: string): string | null => {
    const match = url.match(/spotify\.com\/(track|album|playlist)\/([a-zA-Z0-9]+)/);
    if (match) {
      return `https://open.spotify.com/embed/${match[1]}/${match[2]}`;
    }
    return null;
  };

  const handleMinimize = useCallback(async () => {
    try {
      setError(null);
      await invoke('minimize_to_dot');
      setWindowState(prev => ({ ...prev, isMinimized: true }));
    } catch (error) {
      console.error('Failed to minimize:', error);
      setError('Failed to minimize window');
    }
  }, []);



  const handleToggleMute = useCallback(async () => {
    try {
      setError(null);
      const newMuteState = !windowState.isMuted;
      await invoke('toggle_system_audio', { mute: newMuteState });
      setWindowState(prev => ({ ...prev, isMuted: newMuteState }));
    } catch (error) {
      console.error('Failed to toggle mute:', error);
      setError('Failed to toggle audio mute');
    }
  }, [windowState.isMuted]);

  const handleClose = useCallback(async () => {
    try {
      const window = getCurrentWindow();
      await window.close();
    } catch (error) {
      console.error('Failed to close window:', error);
      setError('Failed to close window');
    }
  }, []);

  const handleIframeLoad = useCallback((service: 'youtube' | 'spotify') => {
    setLoadingStates(prev => ({ ...prev, [service]: false }));
  }, []);

  return (
    <div className="app">
      {/* Error Display */}
      {error && (
        <div className="error-banner">
          <span>{error}</span>
          <button onClick={() => setError(null)}>✕</button>
        </div>
      )}

      {/* Custom Window Controls */}
      <div className="window-controls">
        <button
          className="control-btn mute-btn"
          onClick={handleToggleMute}
          title={windowState.isMuted ? 'Unmute' : 'Mute'}
          disabled={windowState.isMinimized}
        >
          {windowState.isMuted ? '🔇' : '🔊'}
        </button>
        <button
          className="control-btn minimize-btn"
          onClick={handleMinimize}
          title="Minimize to dot"
        >
          ⚫
        </button>
        <button
          className="control-btn close-btn"
          onClick={handleClose}
          title="Close"
        >
          ✕
        </button>
      </div>

      {/* Split View Container */}
      <div className="split-container">
        <div className="view-panel youtube-panel">
          <div className="panel-header">
            <h3>YouTube</h3>
            <div className="panel-controls">
              <button
                className="control-btn"
                onClick={() => setAutoPlay(!autoPlay)}
                title={autoPlay ? 'Disable Auto-play' : 'Enable Auto-play'}
              >
                {autoPlay ? '⏸️' : '▶️'}
              </button>
            </div>
          </div>
          <div className={`webview-container ${!loadingStates.youtube ? 'loaded' : ''}`}>
            <iframe
              ref={youtubeRef}
              src="https://www.youtube.com/embed"
              title="YouTube"
              className="webview"
              allow="autoplay; encrypted-media; picture-in-picture; fullscreen"
              allowFullScreen
              onLoad={() => handleIframeLoad('youtube')}
            />
          </div>
        </div>

        <div className="view-panel spotify-panel">
          <div className="panel-header">
            <h3>Spotify</h3>
            <div className="panel-controls">
              {currentVideo && (
                <span className="current-track">
                  {currentVideo.platform === 'Spotify' ? '🎵' : '🎬'} Playing
                </span>
              )}
            </div>
          </div>
          <div className={`webview-container ${!loadingStates.spotify ? 'loaded' : ''}`}>
            <iframe
              ref={spotifyRef}
              src="https://open.spotify.com/embed"
              title="Spotify"
              className="webview"
              allow="autoplay; encrypted-media; fullscreen"
              onLoad={() => handleIframeLoad('spotify')}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
