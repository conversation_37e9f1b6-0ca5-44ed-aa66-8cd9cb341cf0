{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 15366803983864524774], [16702348383442838006, "build_script_build", false, 14133164338135172556], [4155399759509430981, "build_script_build", false, 4768900150953408864]], "local": [{"RerunIfChanged": {"output": "release\\build\\ysviewer-f600b12b6f540929\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}