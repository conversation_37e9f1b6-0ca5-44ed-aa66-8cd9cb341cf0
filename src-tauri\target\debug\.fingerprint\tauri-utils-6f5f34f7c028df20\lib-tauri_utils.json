{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 11583813680109437787, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 12267592131434969007], [3060637413840920116, "proc_macro2", false, 5479763114165989740], [3150220818285335163, "url", false, 13881185444843535089], [3191507132440681679, "serde_untagged", false, 1067479899076777771], [4071963112282141418, "serde_with", false, 6097332014948828234], [4899080583175475170, "semver", false, 6395737144516353431], [5986029879202738730, "log", false, 11329875774177104672], [6606131838865521726, "ctor", false, 1190915132638082049], [6913375703034175521, "schemars", false, 6206245101019458460], [7170110829644101142, "json_patch", false, 9674569900180521461], [8319709847752024821, "uuid", false, 11798349677673730365], [9010263965687315507, "http", false, 7531794789693008200], [9451456094439810778, "regex", false, 15096131397074870431], [9556762810601084293, "brotli", false, 5690539569629040021], [9689903380558560274, "serde", false, 7128043410522221942], [10806645703491011684, "thiserror", false, 2502066569376588129], [11655476559277113544, "cargo_metadata", false, 12661016117091233615], [11989259058781683633, "dunce", false, 4683714416206784932], [13625485746686963219, "anyhow", false, 13352825885805927492], [14232843520438415263, "html5ever", false, 4692013504664358971], [15088007382495681292, "kuchiki", false, 623655529314365881], [15367738274754116744, "serde_json", false, 9701281561460897278], [15609422047640926750, "toml", false, 15626852650080321713], [15622660310229662834, "walkdir", false, 3834500685285115626], [15932120279885307830, "memchr", false, 11792158447639600885], [17146114186171651583, "infer", false, 9506308886190635682], [17155886227862585100, "glob", false, 9965666631543504377], [17186037756130803222, "phf", false, 7253157634935562955], [17990358020177143287, "quote", false, 3955101340036112161]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-6f5f34f7c028df20\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}