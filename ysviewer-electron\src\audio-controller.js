const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

class AudioController {
  constructor() {
    this.isMuted = false;
    this.platform = process.platform;
  }

  async toggleMute() {
    try {
      if (this.platform === 'win32') {
        return await this.toggleMuteWindows();
      } else if (this.platform === 'darwin') {
        return await this.toggleMuteMacOS();
      } else if (this.platform === 'linux') {
        return await this.toggleMuteLinux();
      } else {
        throw new Error(`Unsupported platform: ${this.platform}`);
      }
    } catch (error) {
      console.error('Failed to toggle mute:', error);
      throw error;
    }
  }

  async toggleMuteWindows() {
    try {
      // PowerShell script to toggle system audio mute
      const script = `
        Add-Type -TypeDefinition @"
        using System;
        using System.Runtime.InteropServices;
        public class Audio {
            [DllImport("user32.dll")]
            public static extern void keybd_event(byte bVk, byte bScan, int dwFlags, int dwExtraInfo);
            public static void Mute() {
                keybd_event(0xAD, 0, 0, 0); // VK_VOLUME_MUTE
                keybd_event(0xAD, 0, 2, 0); // Key up
            }
        }
"@
        [Audio]::Mute()
      `;

      await execAsync(`powershell -Command "${script}"`);
      this.isMuted = !this.isMuted;
      
      console.log(`Audio ${this.isMuted ? 'muted' : 'unmuted'} on Windows`);
      return this.isMuted;
    } catch (error) {
      console.error('Windows audio toggle failed:', error);
      throw error;
    }
  }

  async toggleMuteMacOS() {
    try {
      // Get current volume to determine if muted
      const { stdout: currentVolume } = await execAsync('osascript -e "output volume of (get volume settings)"');
      const volume = parseInt(currentVolume.trim());
      
      if (volume > 0) {
        // Mute by setting volume to 0
        await execAsync('osascript -e "set volume output volume 0"');
        this.isMuted = true;
      } else {
        // Unmute by setting volume to 50
        await execAsync('osascript -e "set volume output volume 50"');
        this.isMuted = false;
      }
      
      console.log(`Audio ${this.isMuted ? 'muted' : 'unmuted'} on macOS`);
      return this.isMuted;
    } catch (error) {
      console.error('macOS audio toggle failed:', error);
      throw error;
    }
  }

  async toggleMuteLinux() {
    try {
      // Try different audio systems
      try {
        // PulseAudio
        await execAsync('pactl set-sink-mute @DEFAULT_SINK@ toggle');
      } catch (pulseError) {
        try {
          // ALSA
          await execAsync('amixer -D pulse sset Master toggle');
        } catch (alsaError) {
          throw new Error('No supported audio system found (tried PulseAudio and ALSA)');
        }
      }
      
      this.isMuted = !this.isMuted;
      console.log(`Audio ${this.isMuted ? 'muted' : 'unmuted'} on Linux`);
      return this.isMuted;
    } catch (error) {
      console.error('Linux audio toggle failed:', error);
      throw error;
    }
  }

  async setMute(mute) {
    if (mute === this.isMuted) {
      return this.isMuted; // Already in desired state
    }
    
    return await this.toggleMute();
  }

  async getVolumeLevel() {
    try {
      if (this.platform === 'win32') {
        return await this.getVolumeWindows();
      } else if (this.platform === 'darwin') {
        return await this.getVolumeMacOS();
      } else if (this.platform === 'linux') {
        return await this.getVolumeLinux();
      }
    } catch (error) {
      console.error('Failed to get volume level:', error);
      return 50; // Default fallback
    }
  }

  async getVolumeWindows() {
    try {
      const script = `
        Add-Type -AssemblyName System.Windows.Forms
        $wmp = New-Object -ComObject WMPlayer.OCX
        [Math]::Round($wmp.settings.volume)
      `;
      
      const { stdout } = await execAsync(`powershell -Command "${script}"`);
      return parseInt(stdout.trim()) || 50;
    } catch (error) {
      return 50;
    }
  }

  async getVolumeMacOS() {
    try {
      const { stdout } = await execAsync('osascript -e "output volume of (get volume settings)"');
      return parseInt(stdout.trim()) || 50;
    } catch (error) {
      return 50;
    }
  }

  async getVolumeLinux() {
    try {
      const { stdout } = await execAsync('pactl get-sink-volume @DEFAULT_SINK@');
      const match = stdout.match(/(\d+)%/);
      return match ? parseInt(match[1]) : 50;
    } catch (error) {
      return 50;
    }
  }

  async setVolume(level) {
    try {
      const clampedLevel = Math.max(0, Math.min(100, level));
      
      if (this.platform === 'win32') {
        await this.setVolumeWindows(clampedLevel);
      } else if (this.platform === 'darwin') {
        await this.setVolumeMacOS(clampedLevel);
      } else if (this.platform === 'linux') {
        await this.setVolumeLinux(clampedLevel);
      }
      
      this.isMuted = clampedLevel === 0;
      return clampedLevel;
    } catch (error) {
      console.error('Failed to set volume:', error);
      throw error;
    }
  }

  async setVolumeWindows(level) {
    const script = `
      Add-Type -AssemblyName System.Windows.Forms
      $wmp = New-Object -ComObject WMPlayer.OCX
      $wmp.settings.volume = ${level}
    `;
    
    await execAsync(`powershell -Command "${script}"`);
  }

  async setVolumeMacOS(level) {
    await execAsync(`osascript -e "set volume output volume ${level}"`);
  }

  async setVolumeLinux(level) {
    await execAsync(`pactl set-sink-volume @DEFAULT_SINK@ ${level}%`);
  }

  getMuteState() {
    return this.isMuted;
  }
}

module.exports = AudioController;
