use crate::s3_client::S3Client;
use chrono::{DateTime, Utc};
use rand::seq::SliceRandom;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::error::Error;
use std::fmt;

#[derive(Debug)]
pub struct PlaylistError(String);

impl fmt::Display for PlaylistError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        write!(f, "Playlist error: {}", self.0)
    }
}

impl Error for PlaylistError {}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoEntry {
    pub url: String,
    pub platform: Platform,
    pub play_count_today: u32,
    pub last_played: Option<DateTime<Utc>>,
    pub duration_watched: f64, // Percentage watched (0.0 to 1.0)
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum Platform {
    YouTube,
    Spotify,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PlaylistData {
    pub mandatory: Vec<VideoEntry>,
    pub random_pool: Vec<VideoEntry>,
    pub last_updated: DateTime<Utc>,
    pub daily_stats: HashMap<String, DailyStats>, // Date -> Stats
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DailyStats {
    pub mandatory_plays: u32,
    pub random_plays: u32,
    pub date: String,
}

pub struct PlaylistManager {
    s3_client: S3Client,
    playlist_data: PlaylistData,
}

impl PlaylistManager {
    pub async fn new() -> Result<Self, PlaylistError> {
        let s3_client = S3Client::new().await
            .map_err(|e| PlaylistError(format!("Failed to create S3 client: {}", e)))?;

        let mut manager = Self {
            s3_client,
            playlist_data: PlaylistData {
                mandatory: Vec::new(),
                random_pool: Vec::new(),
                last_updated: Utc::now(),
                daily_stats: HashMap::new(),
            },
        };

        manager.load_playlist().await?;
        Ok(manager)
    }

    async fn load_playlist(&mut self) -> Result<(), PlaylistError> {
        match self.s3_client.download_file("video_list.txt").await {
            Ok(content) => {
                self.parse_playlist_content(&content)?;
            }
            Err(_) => {
                // If file doesn't exist, create from local file
                if let Ok(local_content) = tokio::fs::read_to_string("video_list.txt").await {
                    self.parse_playlist_content(&local_content)?;
                    self.save_playlist().await?;
                } else {
                    return Err(PlaylistError("No playlist file found locally or in S3".to_string()));
                }
            }
        }
        Ok(())
    }

    fn parse_playlist_content(&mut self, content: &str) -> Result<(), PlaylistError> {
        let lines: Vec<&str> = content.lines().collect();
        let mut current_section = "";
        
        for line in lines {
            let line = line.trim();
            if line.is_empty() {
                continue;
            }
            
            if line.starts_with("Mandatory:") {
                current_section = "mandatory";
                continue;
            } else if line.starts_with("Randomly played:") {
                current_section = "random";
                continue;
            }
            
            if line.starts_with("http") {
                let platform = if line.contains("youtube.com") {
                    Platform::YouTube
                } else if line.contains("spotify.com") {
                    Platform::Spotify
                } else {
                    continue; // Skip unknown platforms
                };
                
                let entry = VideoEntry {
                    url: line.to_string(),
                    platform,
                    play_count_today: 0,
                    last_played: None,
                    duration_watched: 0.0,
                };
                
                match current_section {
                    "mandatory" => self.playlist_data.mandatory.push(entry),
                    "random" => self.playlist_data.random_pool.push(entry),
                    _ => {}
                }
            }
        }
        
        Ok(())
    }

    pub async fn save_playlist(&self) -> Result<(), PlaylistError> {
        let json_content = serde_json::to_string_pretty(&self.playlist_data)
            .map_err(|e| PlaylistError(format!("Failed to serialize playlist: {}", e)))?;

        self.s3_client.upload_file("playlist_data.json", &json_content).await
            .map_err(|e| PlaylistError(format!("Failed to upload playlist data: {}", e)))?;

        Ok(())
    }

    pub fn get_next_mandatory_video(&mut self) -> Option<VideoEntry> {
        let _today = Utc::now().format("%Y-%m-%d").to_string();
        
        // Find videos that need to be played (less than 3 times today)
        for entry in &mut self.playlist_data.mandatory {
            if entry.play_count_today < 3 {
                entry.play_count_today += 1;
                entry.last_played = Some(Utc::now());
                return Some(entry.clone());
            }
        }
        
        None
    }

    pub fn get_random_videos_for_today(&mut self) -> Vec<VideoEntry> {
        let mut rng = rand::thread_rng();
        let total_random = self.playlist_data.random_pool.len();
        let videos_to_play = (total_random as f64 * 0.7).ceil() as usize;
        
        let mut selected: Vec<VideoEntry> = self.playlist_data.random_pool
            .choose_multiple(&mut rng, videos_to_play)
            .cloned()
            .collect();
        
        // Mark as played
        for entry in &mut selected {
            entry.last_played = Some(Utc::now());
        }
        
        selected
    }

    pub fn reset_daily_counters(&mut self) {
        for entry in &mut self.playlist_data.mandatory {
            entry.play_count_today = 0;
        }
        
        let today = Utc::now().format("%Y-%m-%d").to_string();
        self.playlist_data.daily_stats.insert(today.clone(), DailyStats {
            mandatory_plays: 0,
            random_plays: 0,
            date: today,
        });
    }

    pub fn should_play_video(&self, entry: &VideoEntry) -> bool {
        // Check if video should be played based on completion percentage
        entry.duration_watched < 0.95 // Play if less than 95% watched
    }

    pub fn update_watch_progress(&mut self, url: &str, progress: f64) {
        // Update progress for mandatory videos
        for entry in &mut self.playlist_data.mandatory {
            if entry.url == url {
                entry.duration_watched = progress;
                return;
            }
        }
        
        // Update progress for random videos
        for entry in &mut self.playlist_data.random_pool {
            if entry.url == url {
                entry.duration_watched = progress;
                return;
            }
        }
    }

    pub fn get_mandatory_videos(&self) -> &Vec<VideoEntry> {
        &self.playlist_data.mandatory
    }

    pub fn get_random_videos(&self) -> &Vec<VideoEntry> {
        &self.playlist_data.random_pool
    }
}
