{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 18018630359880048803, "deps": [[2671782512663819132, "tauri_utils", false, 10654325798149455431], [10806952569398136823, "tauri_build", false, 7950221822004516029], [13077543566650298139, "heck", false, 3748249671941887103], [17155886227862585100, "glob", false, 2426092903684684966]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-be05ada33b5f90c2\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}