{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 5920070151163404031, "deps": [[2671782512663819132, "tauri_utils", false, 17972510036500744830], [3060637413840920116, "proc_macro2", false, 5479763114165989740], [4974441333307933176, "syn", false, 5462005526711445503], [13077543566650298139, "heck", false, 8423826346643455051], [14455244907590647360, "tauri_codegen", false, 13947698177040231520], [17990358020177143287, "quote", false, 3955101340036112161]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-27cd3a1db9b6c233\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}