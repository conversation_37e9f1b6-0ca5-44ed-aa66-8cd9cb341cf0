{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 5920070151163404031, "deps": [[2671782512663819132, "tauri_utils", false, 10654325798149455431], [3060637413840920116, "proc_macro2", false, 5994116968594339474], [4974441333307933176, "syn", false, 16691343439771321221], [13077543566650298139, "heck", false, 3748249671941887103], [14455244907590647360, "tauri_codegen", false, 11857760539992996375], [17990358020177143287, "quote", false, 2071227603722890861]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-c049dfcad067ec13\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}