{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 4397840711112790432, "deps": [[40386456601120721, "percent_encoding", false, 10812965922734052691], [1200537532907108615, "url<PERSON><PERSON>n", false, 16584255186286395320], [2013030631243296465, "webview2_com", false, 1600957986667950261], [2671782512663819132, "tauri_utils", false, 12805397759495270397], [3150220818285335163, "url", false, 18237341568847020706], [3331586631144870129, "getrandom", false, 9005361582179647630], [4143744114649553716, "raw_window_handle", false, 14094938929688442670], [4494683389616423722, "muda", false, 175997165283566686], [4919829919303820331, "serialize_to_javascript", false, 8340926629026116621], [5986029879202738730, "log", false, 13632052554582360681], [6089812615193535349, "tauri_runtime", false, 2844918446369283321], [7573826311589115053, "tauri_macros", false, 11394077938681386856], [9010263965687315507, "http", false, 6714799978864066278], [9689903380558560274, "serde", false, 15994608536807373832], [10229185211513642314, "mime", false, 6695922017499859830], [10806645703491011684, "thiserror", false, 3098247422307318278], [11599800339996261026, "tauri_runtime_wry", false, 9427206484102007827], [11989259058781683633, "dunce", false, 5696898754074390099], [12393800526703971956, "tokio", false, 10525913411424314441], [12565293087094287914, "window_vibrancy", false, 15821566097003409498], [12986574360607194341, "serde_repr", false, 16984180773121099245], [13077543566650298139, "heck", false, 15312694027487804466], [13625485746686963219, "anyhow", false, 5150798913309496272], [14039947826026167952, "build_script_build", false, 15366803983864524774], [14585479307175734061, "windows", false, 13463505825239044991], [15367738274754116744, "serde_json", false, 12128679407120187727], [16928111194414003569, "dirs", false, 11244520800577409181], [17155886227862585100, "glob", false, 12817926012342817574]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-9f0bc94890fdba19\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}