{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 4397840711112790432, "deps": [[40386456601120721, "percent_encoding", false, 9616258179371068025], [1200537532907108615, "url<PERSON><PERSON>n", false, 15815602450067384781], [2013030631243296465, "webview2_com", false, 4629790111290470228], [2671782512663819132, "tauri_utils", false, 13339082887116487304], [3150220818285335163, "url", false, 2859621893326576119], [3331586631144870129, "getrandom", false, 5557808268446994394], [4143744114649553716, "raw_window_handle", false, 10639854346359357115], [4494683389616423722, "muda", false, 17255261831026719703], [4919829919303820331, "serialize_to_javascript", false, 11178203941805273195], [5986029879202738730, "log", false, 11329875774177104672], [6089812615193535349, "tauri_runtime", false, 13014963732880651884], [7573826311589115053, "tauri_macros", false, 16331574570395059554], [9010263965687315507, "http", false, 7531794789693008200], [9689903380558560274, "serde", false, 4745128726568703706], [10229185211513642314, "mime", false, 221948321356653791], [10806645703491011684, "thiserror", false, 2502066569376588129], [11599800339996261026, "tauri_runtime_wry", false, 14007083258765558916], [11989259058781683633, "dunce", false, 4683714416206784932], [12393800526703971956, "tokio", false, 2299465703854512292], [12565293087094287914, "window_vibrancy", false, 2277456294672581122], [12986574360607194341, "serde_repr", false, 13683033847422859446], [13077543566650298139, "heck", false, 8423826346643455051], [13625485746686963219, "anyhow", false, 13352825885805927492], [14039947826026167952, "build_script_build", false, 14637322225222829], [14585479307175734061, "windows", false, 16597157699585629547], [15367738274754116744, "serde_json", false, 7736598212815895422], [16928111194414003569, "dirs", false, 2297253392593602267], [17155886227862585100, "glob", false, 9965666631543504377]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-a0476ef6268a616e\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}