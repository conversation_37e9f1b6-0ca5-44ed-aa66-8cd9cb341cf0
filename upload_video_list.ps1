# YSViewer Video List S3 Upload Script for Windows PowerShell
# This script uploads video_list.txt to the S3 bucket for the YSViewer application

param(
    [switch]$Download
)

# AWS Configuration
$AWS_ACCESS_KEY_ID = "********************"
$AWS_SECRET_ACCESS_KEY = "/nsxBa33IboJNAEjPfrJZUdczS3FFBbEUG4BQg3T"
$AWS_REGION = "eu-west-1"
$BUCKET_NAME = "ysviewertests"
$FILE_KEY = "video_list.txt"
$LOCAL_FILE_PATH = "video_list.txt"

function Test-AWSCLIInstalled {
    try {
        $null = Get-Command aws -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

function Install-AWSCLI {
    Write-Host "AWS CLI not found. Installing..." -ForegroundColor Yellow
    
    try {
        # Try to install via winget first
        winget install Amazon.AWSCLI
        Write-Host "AWS CLI installed successfully via winget!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Failed to install via winget. Please install AWS CLI manually:" -ForegroundColor Red
        Write-Host "https://aws.amazon.com/cli/" -ForegroundColor Cyan
        return $false
    }
}

function Set-AWSCredentials {
    $env:AWS_ACCESS_KEY_ID = $AWS_ACCESS_KEY_ID
    $env:AWS_SECRET_ACCESS_KEY = $AWS_SECRET_ACCESS_KEY
    $env:AWS_DEFAULT_REGION = $AWS_REGION
}

function Upload-VideoList {
    Write-Host "🎵 YSViewer Video List S3 Upload Tool" -ForegroundColor Cyan
    Write-Host "=" * 40 -ForegroundColor Cyan
    
    # Check if local file exists
    if (-not (Test-Path $LOCAL_FILE_PATH)) {
        Write-Host "❌ Error: $LOCAL_FILE_PATH not found in current directory" -ForegroundColor Red
        return $false
    }
    
    # Check if AWS CLI is installed
    if (-not (Test-AWSCLIInstalled)) {
        if (-not (Install-AWSCLI)) {
            return $false
        }
    }
    
    # Set AWS credentials
    Set-AWSCredentials
    
    try {
        Write-Host "Uploading $LOCAL_FILE_PATH to s3://$BUCKET_NAME/$FILE_KEY" -ForegroundColor Yellow

        # Upload file to S3 with public read access
        $result = aws s3 cp $LOCAL_FILE_PATH "s3://$BUCKET_NAME/$FILE_KEY" --region $AWS_REGION --acl public-read 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Upload successful!" -ForegroundColor Green
            
            # Verify upload
            $verifyResult = aws s3 ls "s3://$BUCKET_NAME/$FILE_KEY" --region $AWS_REGION 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ File verified in S3 bucket" -ForegroundColor Green
                Write-Host "📊 File info: $verifyResult" -ForegroundColor Cyan

                # Show public URL
                $publicUrl = "https://$BUCKET_NAME.s3.$AWS_REGION.amazonaws.com/$FILE_KEY"
                Write-Host "🌐 Public URL: $publicUrl" -ForegroundColor Cyan
            }
            else {
                Write-Host "⚠️  Warning: Could not verify file upload" -ForegroundColor Yellow
            }
            
            return $true
        }
        else {
            Write-Host "❌ Upload failed: $result" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error during upload: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Download-VideoList {
    Write-Host "🎵 YSViewer Video List S3 Download Tool" -ForegroundColor Cyan
    Write-Host "=" * 40 -ForegroundColor Cyan
    
    # Check if AWS CLI is installed
    if (-not (Test-AWSCLIInstalled)) {
        if (-not (Install-AWSCLI)) {
            return $false
        }
    }
    
    # Set AWS credentials
    Set-AWSCredentials
    
    try {
        Write-Host "Downloading $FILE_KEY from s3://$BUCKET_NAME/" -ForegroundColor Yellow
        
        # Download file from S3
        $result = aws s3 cp "s3://$BUCKET_NAME/$FILE_KEY" - --region $AWS_REGION 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "📄 Current content in S3:" -ForegroundColor Cyan
            Write-Host ("-" * 50) -ForegroundColor Gray
            Write-Host $result -ForegroundColor White
            Write-Host ("-" * 50) -ForegroundColor Gray
            return $true
        }
        else {
            Write-Host "❌ Download failed: $result" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error during download: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main execution
if ($Download) {
    $success = Download-VideoList
}
else {
    $success = Upload-VideoList
}

if ($success) {
    Write-Host "`n✅ Operation completed successfully!" -ForegroundColor Green
    exit 0
}
else {
    Write-Host "`n❌ Operation failed!" -ForegroundColor Red
    exit 1
}
