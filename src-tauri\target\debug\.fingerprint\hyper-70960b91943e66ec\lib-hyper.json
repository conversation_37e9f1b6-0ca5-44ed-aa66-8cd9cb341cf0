{"rustc": 1842507548689473721, "features": "[\"client\", \"h2\", \"http1\", \"http2\", \"runtime\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 15657897354478470176, "path": 2194414849061550458, "deps": [[784494742817713399, "tower_service", false, 14910546545688947472], [1569313478171189446, "want", false, 16619601685828828773], [1811549171721445101, "futures_channel", false, 6009155789980088634], [1906322745568073236, "pin_project_lite", false, 4306693265828279487], [4405182208873388884, "http", false, 7059964791119977083], [6163892036024256188, "httparse", false, 5246480150811132136], [6304235478050270880, "httpdate", false, 17657379510882319819], [7620660491849607393, "futures_core", false, 3448932696603660374], [7695812897323945497, "itoa", false, 7007082549211902358], [8606274917505247608, "tracing", false, 16136798546607281271], [8915503303801890683, "http_body", false, 17404688330060696912], [10629569228670356391, "futures_util", false, 364271368291622714], [12393800526703971956, "tokio", false, 2299465703854512292], [12614995553916589825, "socket2", false, 14620094094055974063], [13809605890706463735, "h2", false, 14598998268569857384], [16066129441945555748, "bytes", false, 13705032661803241633]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-70960b91943e66ec\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}