{"rustc": 1842507548689473721, "features": "[\"std\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 15657897354478470176, "path": 15456986552771759525, "deps": [[2828590642173593838, "cfg_if", false, 3818643425445260427]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\getrandom-d9703472c1db5a1c\\dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}