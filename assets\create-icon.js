const fs = require('fs');
const path = require('path');

// Create a simple 16x16 PNG icon programmatically
function createSimpleIcon() {
  // Simple PNG header for 16x16 image
  const width = 16;
  const height = 16;
  
  // Create a simple green square icon
  const pngData = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x10, // Width: 16
    0x00, 0x00, 0x00, 0x10, // Height: 16
    0x08, 0x02, 0x00, 0x00, 0x00, // Bit depth: 8, Color type: 2 (RGB), Compression: 0, Filter: 0, Interlace: 0
    0x90, 0x91, 0x68, 0x36, // CRC
    // Add more PNG data here for a complete image...
  ]);
  
  // For simplicity, let's create a basic ICO file instead
  const icoData = Buffer.from([
    0x00, 0x00, // Reserved
    0x01, 0x00, // Type: 1 (ICO)
    0x01, 0x00, // Number of images: 1
    0x10,       // Width: 16
    0x10,       // Height: 16
    0x00,       // Color count: 0 (no palette)
    0x00,       // Reserved
    0x01, 0x00, // Color planes: 1
    0x20, 0x00, // Bits per pixel: 32
    0x00, 0x04, 0x00, 0x00, // Image size: 1024 bytes
    0x16, 0x00, 0x00, 0x00, // Image offset: 22
    // Add 1024 bytes of image data (16x16x4 bytes per pixel)
    ...Array(1024).fill(0x4C) // Fill with green color
  ]);
  
  return icoData;
}

// Create the icon file
const iconData = createSimpleIcon();
fs.writeFileSync(path.join(__dirname, 'icon.ico'), iconData);
fs.writeFileSync(path.join(__dirname, 'icon.png'), iconData);

console.log('Simple icon files created');
