{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 7200122862893550635, "deps": [[2671782512663819132, "tauri_utils", false, 13339082887116487304], [3150220818285335163, "url", false, 2859621893326576119], [4143744114649553716, "raw_window_handle", false, 10639854346359357115], [6089812615193535349, "build_script_build", false, 6189595636010507558], [7606335748176206944, "dpi", false, 11872213363386873584], [9010263965687315507, "http", false, 7531794789693008200], [9689903380558560274, "serde", false, 4745128726568703706], [10806645703491011684, "thiserror", false, 2502066569376588129], [14585479307175734061, "windows", false, 16597157699585629547], [15367738274754116744, "serde_json", false, 7736598212815895422], [16727543399706004146, "cookie", false, 3243518960846693963]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-9b9354c43eafb234\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}