{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 11583813680109437787, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 16584255186286395320], [3150220818285335163, "url", false, 18237341568847020706], [3191507132440681679, "serde_untagged", false, 3435968453478629070], [4071963112282141418, "serde_with", false, 3955397989189260298], [4899080583175475170, "semver", false, 9799534517135138466], [5986029879202738730, "log", false, 13632052554582360681], [6606131838865521726, "ctor", false, 4338911332435157170], [7170110829644101142, "json_patch", false, 3100373947021369702], [8319709847752024821, "uuid", false, 3087173596082498765], [9010263965687315507, "http", false, 6714799978864066278], [9451456094439810778, "regex", false, 17860234045684177748], [9556762810601084293, "brotli", false, 1982111743762697293], [9689903380558560274, "serde", false, 15994608536807373832], [10806645703491011684, "thiserror", false, 3098247422307318278], [11989259058781683633, "dunce", false, 5696898754074390099], [13625485746686963219, "anyhow", false, 5150798913309496272], [15367738274754116744, "serde_json", false, 12128679407120187727], [15609422047640926750, "toml", false, 15593624970636661822], [15622660310229662834, "walkdir", false, 2321144619203789183], [15932120279885307830, "memchr", false, 15625472333890300458], [17146114186171651583, "infer", false, 12712991461897290902], [17155886227862585100, "glob", false, 12817926012342817574], [17186037756130803222, "phf", false, 13869232948258792682]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-5f2305b3e6c2615d\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}