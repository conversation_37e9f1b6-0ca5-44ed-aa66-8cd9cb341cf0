{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463************, "path": 15459641028755272627, "deps": [[2671782512663819132, "tauri_utils", false, 17972510036500744830], [3060637413840920116, "proc_macro2", false, 5479763114165989740], [3150220818285335163, "url", false, 13881185444843535089], [4899080583175475170, "semver", false, 6395737144516353431], [4974441333307933176, "syn", false, 5462005526711445503], [7170110829644101142, "json_patch", false, 9674569900180521461], [7392050791754369441, "ico", false, 11330277972340411827], [8319709847752024821, "uuid", false, 11798349677673730365], [9556762810601084293, "brotli", false, 5690539569629040021], [9689903380558560274, "serde", false, 7128043410522221942], [9857275760291862238, "sha2", false, 3463983067125123896], [10806645703491011684, "thiserror", false, 2502066569376588129], [12687914511023397207, "png", false, 9225335520579775325], [13077212702700853852, "base64", false, 3653853219338407452], [15367738274754116744, "serde_json", false, 9701281561460897278], [15622660310229662834, "walkdir", false, 3834500685285115626], [17990358020177143287, "quote", false, 3955101340036112161]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-085e50576154f8bf\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}