#!/usr/bin/env python3
"""
Script to upload video_list.txt to S3 bucket for YSViewer application
"""

import boto3
import os
import sys
from botocore.exceptions import ClientError, NoCredentialsError

# AWS Configuration
AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "/nsxBa33IboJNAEjPfrJZUdczS3FFBbEUG4BQg3T"
AWS_REGION = "eu-west-1"
BUCKET_NAME = "ysviewertests"
FILE_KEY = "video_list.txt"
LOCAL_FILE_PATH = "video_list.txt"

def upload_to_s3():
    """Upload video_list.txt to S3 bucket"""
    
    # Check if local file exists
    if not os.path.exists(LOCAL_FILE_PATH):
        print(f"Error: {LOCAL_FILE_PATH} not found in current directory")
        return False
    
    try:
        # Create S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION
        )
        
        # Read local file
        with open(LOCAL_FILE_PATH, 'r', encoding='utf-8') as file:
            content = file.read()
        
        print(f"Uploading {LOCAL_FILE_PATH} to s3://{BUCKET_NAME}/{FILE_KEY}")
        
        # Upload file to S3 with public read access
        s3_client.put_object(
            Bucket=BUCKET_NAME,
            Key=FILE_KEY,
            Body=content,
            ContentType='text/plain',
            ACL='public-read'
        )
        
        print("✅ Upload successful!")

        # Verify upload by checking if file exists
        try:
            s3_client.head_object(Bucket=BUCKET_NAME, Key=FILE_KEY)
            print("✅ File verified in S3 bucket")

            # Get file info
            response = s3_client.head_object(Bucket=BUCKET_NAME, Key=FILE_KEY)
            file_size = response['ContentLength']
            last_modified = response['LastModified']

            print(f"📊 File size: {file_size} bytes")
            print(f"📅 Last modified: {last_modified}")

            # Show public URL
            public_url = f"https://{BUCKET_NAME}.s3.{AWS_REGION}.amazonaws.com/{FILE_KEY}"
            print(f"🌐 Public URL: {public_url}")

        except ClientError as e:
            print(f"⚠️  Warning: Could not verify file upload: {e}")
        
        return True
        
    except NoCredentialsError:
        print("❌ Error: AWS credentials not found or invalid")
        return False
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'NoSuchBucket':
            print(f"❌ Error: Bucket '{BUCKET_NAME}' does not exist")
        elif error_code == 'AccessDenied':
            print("❌ Error: Access denied. Check your AWS credentials and permissions")
        else:
            print(f"❌ Error: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def download_from_s3():
    """Download and display current video_list.txt from S3"""
    
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            region_name=AWS_REGION
        )
        
        print(f"Downloading {FILE_KEY} from s3://{BUCKET_NAME}/")
        
        response = s3_client.get_object(Bucket=BUCKET_NAME, Key=FILE_KEY)
        content = response['Body'].read().decode('utf-8')
        
        print("📄 Current content in S3:")
        print("-" * 50)
        print(content)
        print("-" * 50)
        
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'NoSuchKey':
            print(f"❌ Error: File '{FILE_KEY}' not found in bucket")
        else:
            print(f"❌ Error downloading file: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main function"""
    print("🎵 YSViewer Video List S3 Upload Tool")
    print("=" * 40)
    
    if len(sys.argv) > 1 and sys.argv[1] == "download":
        success = download_from_s3()
    else:
        success = upload_to_s3()
    
    if success:
        print("\n✅ Operation completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Operation failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
