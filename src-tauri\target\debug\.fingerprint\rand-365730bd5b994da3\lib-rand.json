{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"rand_pcg\", \"small_rng\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 2225463790103693989, "path": 11980200832899857609, "deps": [[1333041802001714747, "rand_chacha", false, 3973972007205127952], [1740877332521282793, "rand_core", false, 17047310795332336802], [5170503507811329045, "getrandom_package", false, 15265811521767547606], [9875507072765444643, "rand_pcg", false, 1411188851159924500]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-365730bd5b994da3\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}